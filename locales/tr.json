{"general": {"password_page": {"login_form_heading": "Parolayı kullanarak mağazaya girin:", "login_password_button": "<PERSON><PERSON><PERSON> k<PERSON> gir", "login_form_password_label": "Pa<PERSON><PERSON>", "login_form_password_placeholder": "Parolanız", "login_form_error": "Yanlış parola!", "login_form_submit": "Gir", "admin_link_html": "Mağaza sahibi misiniz? <a href=\"/admin\" class=\"link underlined-link\">B<PERSON>dan oturum açın</a>", "powered_by_shopify_html": "Bu mağaza {{ shopify }} tarafından desteklenir"}, "social": {"alt_text": {"share_on_facebook": "Facebook'ta paylaş", "share_on_twitter": "X'te paylaş", "share_on_pinterest": "Pinterest'te pin ekle"}, "links": {"twitter": "X (Twitter)", "facebook": "Facebook", "pinterest": "Pinterest", "instagram": "Instagram", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "continue_shopping": "Alışverişe devam et", "pagination": {"label": "<PERSON><PERSON><PERSON><PERSON>", "page": "<PERSON><PERSON> {{ number }}", "next": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON> say<PERSON>"}, "search": {"search": "Ara", "reset": "<PERSON><PERSON> terimini temizle"}, "cart": {"view": "Sepeti görüntüle ({{ count }})", "item_added": "Ürün sepetinize eklendi", "view_empty_cart": "Sepeti görüntüle"}, "share": {"copy_to_clipboard": "Bağlantıyı kopyala", "share_url": "Bağlantı", "success_message": "Bağlantı panoya kopyalandı", "close": "Paylaşımı kapat"}, "slider": {"of": "/", "next_slide": "<PERSON><PERSON><PERSON> ka<PERSON>ı<PERSON>", "previous_slide": "<PERSON>a kaydır", "name": "Kaydırıcı"}}, "newsletter": {"label": "E-posta", "success": "Abone olduğunuz için teşekkür ederiz", "button_label": "<PERSON><PERSON> ol"}, "accessibility": {"skip_to_text": "İçeriğe atla", "close": "Ka<PERSON><PERSON>", "unit_price_separator": "/", "vendor": "Satıcı:", "error": "<PERSON><PERSON>", "refresh_page": "<PERSON><PERSON> seçim ya<PERSON>z sayfanın tamamının yenilenmesine neden olur.", "link_messages": {"new_window": "<PERSON>ni bir pencerede açılır.", "external": "Harici web sitesini açar."}, "loading": "Yükleniyor...", "skip_to_product_info": "<PERSON><PERSON><PERSON><PERSON> bilgisine atla", "total_reviews": "toplam değerlendirme", "star_reviews_info": "{{ rating_value }}/{{ rating_max }} yıldız", "collapsible_content_title": "Daraltılabilir içerik", "complementary_products": "Tamamlayıcı ürünler"}, "blogs": {"article": {"blog": "Blog", "read_more_title": "Dev<PERSON>ı<PERSON><PERSON> okuyun: {{ title }}", "comments": {"one": "{{ count }} yorum", "other": "{{ count }} yorum"}, "moderated": "Yorumların yayınlanabilmesi için onaylanması gerektiğini lütfen unutmayın.", "comment_form_title": "<PERSON><PERSON>", "name": "Ad", "email": "E-posta", "message": "<PERSON><PERSON>", "post": "<PERSON><PERSON><PERSON>", "back_to_blog": "<PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> makal<PERSON>i <PERSON>", "success": "Yorumunuz başarıyla paylaşıldı! Teşekkür ederiz.", "success_moderated": "Yorumunuz başarıyla paylaşıldı. Blogumuz denetlendiğinden yorumunuzu kısa bir süre sonra yayınlayacağız."}}, "onboarding": {"product_title": "Örnek ürün başlığı", "collection_title": "Koleksiyonunuzun adı"}, "products": {"product": {"add_to_cart": "Sepete ekle", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "on_sale": "İndirim", "quantity": {"label": "<PERSON><PERSON>", "input_label": "{{ product }} i<PERSON>in adet", "increase": "{{ product }} i<PERSON><PERSON> aded<PERSON>", "decrease": "{{ product }} i<PERSON><PERSON> ad<PERSON>", "minimum_of": "Minimum: {{ quantity }}", "maximum_of": "Maksimum: {{ quantity }}", "multiples_of": "<PERSON><PERSON>ş değeri: {{ quantity }}", "in_cart_html": "Sepette: <span class=\"quantity-cart\">{{ quantity }}</span>", "note": "<PERSON><PERSON> kurallarını görüntüle", "min_of": "Minimum {{ quantity }}", "max_of": "Ma<PERSON><PERSON>um {{ quantity }}", "in_cart_aria_label": "Adet (sepetteki: {{ quantity }})"}, "price": {"from_price_html": "Başlangıç fiyatı: {{ price }}", "regular_price": "Normal fiyat", "sale_price": "İndirimli fi<PERSON>t", "unit_price": "<PERSON><PERSON><PERSON>"}, "share": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "sold_out": "Tükendi", "unavailable": "Kullanım dışı", "vendor": "Satıcı", "video_exit_message": "{{ title }} a<PERSON><PERSON> pencerede tam ekran video açar.", "xr_button": "<PERSON><PERSON>ı<PERSON>ı<PERSON> görüntüleyin", "xr_button_label": "Alanınızda görüntüleyin; <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>rılmış gerçeklik penceresinde yüklenir", "pickup_availability": {"view_store_info": "Mağaza bilgilerini görü<PERSON><PERSON><PERSON>in", "check_other_stores": "<PERSON><PERSON><PERSON>alardaki stok durumunu kontrol edin", "pick_up_available": "<PERSON><PERSON><PERSON>ı<PERSON> k<PERSON>bil<PERSON>", "pick_up_available_at_html": "<PERSON><PERSON><PERSON> <span class=\"color-foreground\">{{ location_name }}</span> konumunda kullanılabilir", "pick_up_unavailable_at_html": "<PERSON><PERSON><PERSON> al<PERSON>m <span class=\"color-foreground\">{{ location_name }}</span> konumunda şu anda kullanılamıyor", "unavailable": "<PERSON><PERSON><PERSON> alım stok durumu yüklenemedi", "refresh": "<PERSON><PERSON><PERSON>"}, "media": {"open_media": "Medya {{ index }} modda oynatın", "play_model": "3B Görüntüleyici'yi <PERSON>", "play_video": "<PERSON><PERSON> o<PERSON>t", "gallery_viewer": "<PERSON><PERSON>", "load_image": "Görsel {{ index }} gal<PERSON> görü<PERSON>üley<PERSON> yükleyin", "load_model": "3B Model {{ index }} galeri görüntüleyicide yükleyin", "load_video": "Video {{ index }} gal<PERSON> gö<PERSON>üleyicide oynatın", "image_available": "Görsel {{ index }} artık galeri görüntüleyicide kullanılabilir"}, "nested_label": "{{ parent_title }} için {{ title }}", "view_full_details": "<PERSON><PERSON>m ayrıntıları görüntüle", "shipping_policy_html": "<a href=\"{{ link }}\"><PERSON><PERSON></a>, ödeme sayfa<PERSON>ında hesaplanır.", "choose_options": "Se<PERSON><PERSON><PERSON><PERSON> belirle", "choose_product_options": "{{ product_name }} i<PERSON><PERSON> belirle", "value_unavailable": "{{ option_value }} - Kullanılamıyor", "variant_sold_out_or_unavailable": "Varyasyon tükendi veya kullanılamıyor", "inventory_in_stock": "Stok<PERSON>", "inventory_in_stock_show_count": "Stokta {{ quantity }} adet mevcut", "inventory_low_stock": "Stok düzeyi düşük", "inventory_low_stock_show_count": "Stok düzeyi düşük: {{ quantity }} adet kaldı", "inventory_out_of_stock": "Stokta yok", "inventory_out_of_stock_continue_selling": "Stok<PERSON>", "sku": "SKU", "volume_pricing": {"title": "Toplu Alım Bazlı Fiyatlandırma", "note": "Toplu alım bazlı fiyatlandırma kullanılabilir", "minimum": "{{ quantity }}+", "price_range": "{{ minimum }} - {{ maximum }}", "price_at_each_html": "{{ price }}/adet"}, "product_variants": "<PERSON><PERSON><PERSON><PERSON>", "taxes_included": "<PERSON><PERSON><PERSON><PERSON> dahil.", "duties_included": "Gümrük vergileri dahil.", "duties_and_taxes_included": "Vergiler ve gümrük vergileri dahil."}, "modal": {"label": "<PERSON><PERSON><PERSON> galerisi"}, "facets": {"apply": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "clear_all": "Tümünü kaldır", "from": "En düşük", "filter_and_sort": "Filtrele ve sırala", "filter_by_label": "Filtre:", "filter_button": "Filtrele", "filters_selected": {"one": "{{ count }} se<PERSON><PERSON><PERSON>", "other": "{{ count }} se<PERSON><PERSON><PERSON>"}, "max_price": "En yüksek fiyat: {{ price }}", "product_count": {"one": "{{ count }}/{{ product_count }} ü<PERSON><PERSON><PERSON>", "other": "{{ count }}/{{ product_count }} ü<PERSON><PERSON><PERSON>"}, "product_count_simple": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "reset": "Sıfırla", "sort_button": "S<PERSON>rala", "sort_by_label": "Sıralama ölçütü:", "to": "<PERSON>ü<PERSON>", "clear_filter": "<PERSON><PERSON><PERSON><PERSON> kaldır", "filter_selected_accessibility": "{{ type }} ({{ count }} filtre seçildi)", "show_more": "<PERSON><PERSON> faz<PERSON>", "show_less": "<PERSON><PERSON> a<PERSON> g<PERSON>", "filter_and_operator_subtitle": "Tümünü eşleştir"}}, "templates": {"search": {"no_results": "\"{{ terms }}\" iç<PERSON> sonuç bulunamadı. Ya<PERSON>ım hatası olmadığını doğrulayın veya farklı bir kelime ya da ifade kullanın.", "results_with_count": {"one": "{{ count }} sonuç", "other": "{{ count }} sonuç"}, "title": "<PERSON><PERSON>", "page": "Say<PERSON>", "products": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search_for": "\"{{ terms }}\" i<PERSON><PERSON> arama yap", "results_with_count_and_term": {"one": "\"{{ terms }}\" için {{ count }} son<PERSON><PERSON> bulundu", "other": "\"{{ terms }}\" için {{ count }} son<PERSON><PERSON> bulundu"}, "results_pages_with_count": {"one": "{{ count }} sayfa", "other": "{{ count }} sayfa"}, "results_products_with_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}, "suggestions": "<PERSON><PERSON><PERSON>", "pages": "<PERSON><PERSON><PERSON>", "results_suggestions_with_count": {"one": "{{ count }} <PERSON><PERSON>i", "other": "{{ count }} <PERSON><PERSON>i"}}, "cart": {"cart": "Sepet"}, "contact": {"form": {"name": "Ad", "email": "E-posta", "phone": "Telefon numarası", "comment": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "post_success": "Bizimle iletişime geçtiğiniz için teşekkür ederiz. Mümkün olan en kısa sürede size dönüş yapacağız.", "error_heading": "Lütfen aşağıdakileri düzenleyin:", "title": "İletişim formu"}}, "404": {"title": "Sayfa bulunamadı", "subtext": "404"}}, "sections": {"header": {"announcement": "<PERSON><PERSON><PERSON>", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ count }} <PERSON><PERSON><PERSON><PERSON>"}}, "cart": {"title": "Sepetiniz", "caption": "Sepet ürünleri", "remove_title": "{{ title }} kanalını kaldır", "note": "Siparişe özel talimatlar", "checkout": "Ödeme", "empty": "Sepetiniz boş", "cart_error": "Sepetiniz güncellenirken bir hata oluştu. Lütfen tekrar deneyin.", "cart_quantity_error_html": "Sepetinize bu üründen yalnızca {{ quantity }} adet ekleyebilirsiniz.", "headings": {"product": "<PERSON><PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON><PERSON>", "total": "Toplam", "quantity": "<PERSON><PERSON>", "image": "<PERSON><PERSON><PERSON><PERSON>"}, "update": "<PERSON><PERSON><PERSON><PERSON>", "login": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z var mı?", "paragraph_html": "Daha hızlı ödeme yapmak için <a href=\"{{ link }}\" class=\"link underlined-link\">oturum açın</a>."}, "estimated_total": "<PERSON><PERSON><PERSON>", "new_estimated_total": "<PERSON><PERSON> ta<PERSON>", "duties_and_taxes_included_shipping_at_checkout_with_policy_html": "Vergiler ve gümrük vergileri dahil. İndirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sırasında hesaplanır.", "duties_and_taxes_included_shipping_at_checkout_without_policy": "Vergiler ve gümrük vergileri dahil. İndirimler ve kargo, ödeme sırasında hesaplanır.", "taxes_included_shipping_at_checkout_with_policy_html": "Vergiler dahil. İndirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sırasında hesaplanır.", "taxes_included_shipping_at_checkout_without_policy": "Vergiler dahil. İndi<PERSON>ler ve kargo, ödeme sırasında hesaplanır.", "duties_included_taxes_at_checkout_shipping_at_checkout_with_policy_html": "Gümrük vergileri dahil. Vergiler, indirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sayfasında hesaplanır.", "duties_included_taxes_at_checkout_shipping_at_checkout_without_policy": "Gümrük vergileri dahil. <PERSON><PERSON><PERSON><PERSON>, indir<PERSON><PERSON> ve kargo, ödeme sayfasında hesaplanır.", "taxes_at_checkout_shipping_at_checkout_with_policy_html": "<PERSON><PERSON><PERSON><PERSON>, indirimler ve <a href=\"{{ link }}\">kargo</a>, ödeme sayfasında hesaplanır.", "taxes_at_checkout_shipping_at_checkout_without_policy": "<PERSON><PERSON><PERSON><PERSON>, indir<PERSON><PERSON> ve kargo, ödeme sayfasında hesa<PERSON>."}, "footer": {"payment": "<PERSON><PERSON><PERSON>"}, "featured_blog": {"view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onboarding_title": "Blog gönderisi", "onboarding_content": "Müşterilerinize blog gönderinizin özetini gösterin"}, "featured_collection": {"view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view_all_label": "{{ collection_name }} koleksiyonundaki tüm ürünleri gö<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "collection_list": {"view_all": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "collection_template": {"title": "Koleksiyon", "empty": "<PERSON><PERSON><PERSON><PERSON> bulunamadı", "use_fewer_filters_html": "<PERSON><PERSON> az filtre kullan veya <a class=\"{{ class }}\" href=\"{{ link }}\">tü<PERSON><PERSON><PERSON><PERSON> kaldır</a>"}, "video": {"load_video": "<PERSON><PERSON> yükle: {{ description }}"}, "slideshow": {"load_slide": "<PERSON><PERSON><PERSON>ı yü<PERSON>", "previous_slideshow": "<PERSON><PERSON><PERSON> slayt", "next_slideshow": "<PERSON><PERSON><PERSON> slayt", "pause_slideshow": "<PERSON>layt gösterisini du<PERSON>", "play_slideshow": "Slayt gösterisini oynat", "carousel": "Döngü", "slide": "<PERSON><PERSON><PERSON>"}, "page": {"title": "Say<PERSON> başlığı"}, "announcements": {"previous_announcement": "<PERSON><PERSON><PERSON>", "next_announcement": "<PERSON><PERSON><PERSON>", "carousel": "Carousel", "announcement": "<PERSON><PERSON><PERSON>", "announcement_bar": "<PERSON><PERSON><PERSON>"}, "quick_order_list": {"product_total": "Ürün alt toplamı", "view_cart": "Sepeti görüntüle", "each": "{{ money }}/adet", "product": "<PERSON><PERSON><PERSON><PERSON>", "variant": "<PERSON><PERSON><PERSON><PERSON>", "variant_total": "Varyasyon toplamı", "items_added": {"one": "{{ quantity }} <PERSON><PERSON><PERSON><PERSON>", "other": "{{ quantity }} <PERSON><PERSON><PERSON><PERSON>"}, "items_removed": {"one": "{{ quantity }} ür<PERSON><PERSON> kaldırıldı", "other": "{{ quantity }} ür<PERSON><PERSON> kaldırıldı"}, "product_variants": "<PERSON><PERSON><PERSON><PERSON>", "total_items": "Toplam ürün say<PERSON>ı", "remove_all_items_confirmation": "{{ quantity }} ürü<PERSON><PERSON>n tamamı sepetten kaldırılsın mı?", "remove_all": "Tümünü kaldır", "cancel": "İptal", "remove_all_single_item_confirmation": "1 ürün sepetten kaldırılsın mı?", "min_error": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> i<PERSON>in minimum değer: {{ min }}", "max_error": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> i<PERSON> ma<PERSON>: {{ max }}", "step_error": "<PERSON>u öğ<PERSON>i yalnız<PERSON> {{ step }} değerinde artışlarla ekleyebilirsiniz"}}, "localization": {"country_label": "<PERSON><PERSON><PERSON>/bölge", "language_label": "Dil", "update_language": "<PERSON><PERSON>", "update_country": "<PERSON><PERSON><PERSON>/bö<PERSON> bil<PERSON>", "search": "Ara", "popular_countries_regions": "Popüler ülkeler/bölgeler", "country_results_count": "{{ count }} ü<PERSON><PERSON>/bölge bulundu"}, "customer": {"account": {"title": "<PERSON><PERSON><PERSON>", "details": "<PERSON><PERSON><PERSON>", "view_addresses": "<PERSON><PERSON><PERSON>", "return": "<PERSON><PERSON><PERSON> bilgi<PERSON>ine geri <PERSON>n"}, "account_fallback": "<PERSON><PERSON><PERSON>", "activate_account": {"title": "Hesabı etkinleştirin", "subtext": "Hesabınızı etkinleştirmek için parolanızı oluşturun.", "password": "Pa<PERSON><PERSON>", "password_confirm": "Parolayı doğrula", "submit": "Hesabı etkinleştir", "cancel": "<PERSON><PERSON>"}, "addresses": {"title": "<PERSON><PERSON><PERSON>", "default": "Varsayılan", "add_new": "<PERSON><PERSON> ad<PERSON> ekle", "edit_address": "<PERSON><PERSON><PERSON>", "first_name": "Ad", "last_name": "Soyadı", "company": "Şirket", "address1": "Adres 1", "address2": "Adres 2", "city": "Şehir", "country": "<PERSON><PERSON><PERSON>/bölge", "province": "İl", "zip": "Posta kodu", "phone": "Telefon", "set_default": "Varsayılan adres o<PERSON> a<PERSON>", "add": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "cancel": "İptal Et", "edit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "delete_confirm": "Bu adresi silmek istediğinizden emin misiniz?"}, "log_in": "Oturum aç", "log_out": "<PERSON><PERSON><PERSON><PERSON> kapat", "login_page": {"cancel": "İptal Et", "create_account": "<PERSON><PERSON><PERSON>", "email": "E-posta", "forgot_password": "Parolanızı mı unuttunuz?", "guest_continue": "<PERSON><PERSON>", "guest_title": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> devam edin", "password": "Pa<PERSON><PERSON>", "title": "Oturum aç", "sign_in": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "alternate_provider_separator": "veya"}, "orders": {"title": "Sipariş geçmişi", "order_number": "Sipariş", "order_number_link": "Sipariş numarası {{ number }}", "date": "<PERSON><PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "total": "Toplam", "none": "Hen<PERSON>z sipariş vermediniz."}, "recover_password": {"title": "Parolanızı sıfırlayın", "subtext": "Parolanızı sıfırlamanız için size bir e-posta göndereceğiz", "success": "Size parolanızı güncelleme bağlantısının bulunduğu bir e-posta gönderdik."}, "register": {"title": "<PERSON><PERSON><PERSON>", "first_name": "Ad", "last_name": "Soyadı", "email": "E-posta", "password": "Pa<PERSON><PERSON>", "submit": "Oluştur"}, "reset_password": {"title": "<PERSON>sap <PERSON>ını sıfırlayın", "subtext": "<PERSON><PERSON> bir parola girin", "password": "Pa<PERSON><PERSON>", "password_confirm": "Parolayı doğrula", "submit": "Parolayı sıfırla"}, "order": {"title": "{{ name }} siparişi", "date_html": "<PERSON><PERSON><PERSON><PERSON>: {{ date }}", "cancelled_html": "<PERSON><PERSON><PERSON><PERSON><PERSON>: {{ date }}", "cancelled_reason": "Neden: {{ reason }}", "billing_address": "<PERSON><PERSON>", "payment_status": "<PERSON><PERSON><PERSON>", "shipping_address": "<PERSON><PERSON>", "fulfillment_status": "<PERSON><PERSON><PERSON><PERSON>", "discount": "İndirim", "shipping": "<PERSON><PERSON>", "tax": "<PERSON><PERSON><PERSON>", "product": "<PERSON><PERSON><PERSON><PERSON>", "sku": "SKU", "price": "<PERSON><PERSON><PERSON>", "quantity": "<PERSON><PERSON>", "total": "Toplam", "fulfilled_at_html": "G<PERSON>nderildi: {{ date }}", "track_shipment": "<PERSON><PERSON><PERSON> taki<PERSON> et", "tracking_url": "Takip bağlantısı", "tracking_company": "Kargo <PERSON>", "tracking_number": "Takip numa<PERSON>ı", "subtotal": "Alt toplam", "total_duties": "Gümrük vergileri", "total_refunded": "Para iadesi yapıldı"}}, "gift_cards": {"issued": {"title": "İşte {{ shop }} için {{ value }} tutarındaki hediye kartı bakiyeniz!", "subtext": "Hediye kartınız", "gift_card_code": "Hediye kartı kodu", "shop_link": "Online mağazayı ziyaret edin", "add_to_apple_wallet": "Apple Wallet'a ekle", "qr_image_alt": "QR kodu: Hediye kartını kullanmak için tarayın", "copy_code": "Hediye kartı kodunu kopyala", "expired": "<PERSON><PERSON><PERSON><PERSON> sona erdi", "copy_code_success": "Kod başarıyla kopyalandı", "how_to_use_gift_card": "Hediye kartı kodunu online olarak veya QR kodunu mağazada kullanın", "expiration_date": "Sona erme tarihi: {{ expires_on }}"}}, "recipient": {"form": {"checkbox": "<PERSON><PERSON>u hediye olarak göndermek istiyorum", "email_label": "Alıcı e-postası", "email": "E-posta", "name_label": "Alıcı adı (isteğe bağlı)", "name": "Ad", "message_label": "Mesaj (isteğe bağlı)", "message": "<PERSON><PERSON>", "max_characters": "Maks<PERSON>um {{ max_chars }} karakter", "email_label_optional_for_no_js_behavior": "Alıcı e-postası (isteğe bağlı)", "send_on": "YYYY-AA-GG", "send_on_label": "<PERSON><PERSON> ta<PERSON> (isteğe bağlı)", "expanded": "Hediye kartı alıcısı formu genişletildi", "collapsed": "Hediye kartı alıcısı formu daraltıldı"}}}