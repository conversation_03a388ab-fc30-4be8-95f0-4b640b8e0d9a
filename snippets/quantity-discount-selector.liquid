{% comment %}
  Renders quantity discount selector.
  Accepts:
  - block: {Object} block object.
  - product: {Object} product object.
  - section_id: {String} id of section to which this snippet belongs.

  Usage:
  {% render 'quantity-discount-selector', block: block, product: product, section_id: section.id %}
{% endcomment %}

{%- if block.settings.discount_tiers != blank -%}
  {{ 'component-quantity-discount.css' | asset_url | stylesheet_tag }}
  
  <div class="quantity-discount-wrapper" {{ block.shopify_attributes }}>
    {%- if block.settings.heading != blank -%}
      <h3 class="quantity-discount__heading">{{ block.settings.heading }}</h3>
    {%- endif -%}
    
    {%- assign discount_lines = block.settings.discount_tiers | newline_to_br | split: '<br />' -%}
    {%- assign current_price = product.selected_or_first_available_variant.price -%}
    
    <div class="quantity-discount__options">
      {%- for line in discount_lines -%}
        {%- unless line == blank -%}
          {%- assign parts = line | split: '|' -%}
          {%- if parts.size >= 3 -%}
            {%- assign quantity = parts[0] | strip | plus: 0 -%}
            {%- assign discount_price = parts[1] | strip | plus: 0 -%}
            {%- assign label = parts[2] | strip -%}
            {%- assign total_regular_price = current_price | times: quantity -%}
            {%- assign savings = total_regular_price | minus: discount_price -%}
            {%- assign savings_percentage = savings | times: 100.0 | divided_by: total_regular_price | round -%}
            
            <div class="quantity-discount__option" 
                 data-quantity="{{ quantity }}" 
                 data-price="{{ discount_price }}"
                 data-variant-id="{{ product.selected_or_first_available_variant.id }}">
              <div class="quantity-discount__content">
                <div class="quantity-discount__label">{{ label }}</div>
                <div class="quantity-discount__pricing">
                  <span class="quantity-discount__price">{{ discount_price | money }}</span>
                  {%- if block.settings.show_savings and savings > 0 -%}
                    <span class="quantity-discount__regular-price">{{ total_regular_price | money }}</span>
                    <span class="quantity-discount__savings">{{ savings | money }} {{ 'products.quantity_discount.savings' | t }}</span>
                  {%- endif -%}
                </div>
                <div class="quantity-discount__per-item">
                  {{ 'products.quantity_discount.per_item' | t }} {{ discount_price | divided_by: quantity | money }}
                </div>
              </div>
              <button type="button"
                      class="quantity-discount__button button button--secondary"
                      data-quantity="{{ quantity }}"
                      data-variant-id="{{ product.selected_or_first_available_variant.id }}">
                {{ 'products.quantity_discount.add_to_cart' | t }}
              </button>
            </div>
          {%- endif -%}
        {%- endunless -%}
      {%- endfor -%}
    </div>
  </div>

  <script src="{{ 'quantity-discount.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
